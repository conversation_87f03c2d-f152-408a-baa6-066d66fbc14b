//! # TCP 处理器模块
//!
//! 本模块负责处理 TUN 设备的 TCP 连接，包括连接建立、
//! DNS 封锁检查、隧道映射信息创建等功能。

use std::net::SocketAddr;
use std::pin::Pin;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};

use futures::StreamExt;
use log::{debug, error, warn};
use netstack_lwip::TcpListener;
use tokio::spawn;

use flyshadow_common::tunnel::tunnel_package::PackageProtocol;

use crate::context::context::TunnelContext;
use crate::context::proxy_type::ProxyType;
use crate::mapper::tunnel_mapper_info::TunnelMapperInfo;
use crate::util::process_util::get_process_name_by_port;

use super::config::TunConfig;
use super::error::{error_utils, TunError, TunResult};

/// TCP 连接处理器
///
/// 负责处理来自网络栈的 TCP 连接请求
pub struct TcpHandler {
    /// 隧道上下文
    tunnel_context: Arc<TunnelContext>,
    /// TUN 配置
    config: TunConfig,
}

/// TCP 连接统计信息
#[derive(Debug, Default, Clone)]
pub struct TcpStats {
    /// 总连接数
    pub total_connections: u64,
    /// 活跃连接数
    pub active_connections: u64,
    /// 被封锁的连接数
    pub blocked_connections: u64,
    /// 连接失败数
    pub failed_connections: u64,
}

impl TcpHandler {
    /// 创建新的 TCP 处理器
    ///
    /// # 参数
    /// * `tunnel_context` - 隧道上下文
    /// * `config` - TUN 配置
    ///
    /// # 返回值
    /// 返回新的 TCP 处理器实例
    pub fn new(tunnel_context: Arc<TunnelContext>, config: TunConfig) -> Self {
        Self {
            tunnel_context,
            config,
        }
    }

    /// 启动 TCP 连接处理任务
    ///
    /// # 参数
    /// * `tcp_listener` - TCP 监听器
    ///
    /// # 返回值
    /// 如果启动成功则返回 `Ok(())`，否则返回错误
    pub async fn start_handling(&self, tcp_listener: TcpListener) -> TunResult<()> {
        debug!("启动 TCP 连接处理任务");

        let tunnel_context = self.tunnel_context.clone();
        let config = self.config.clone();

        spawn(async move {
            let mut stats = TcpStats::default();
            let mut tcp_listener = Box::pin(tcp_listener);

            while let Some((stream, local_addr, remote_addr)) = tcp_listener.next().await {
                stats.total_connections += 1;

                // 检查是否为需要封锁的 DNS 加密地址
                let remote_addr_str = remote_addr.to_string();
                if config.is_dns_address_blocked(&remote_addr_str) {
                    debug!("封锁 DNS 加密连接: {}", remote_addr_str);
                    stats.blocked_connections += 1;
                    continue;
                }

                stats.active_connections += 1;

                let tunnel_context_clone = tunnel_context.clone();
                let stats_clone = Arc::new(tokio::sync::Mutex::new(stats.clone()));

                spawn(async move {
                    if let Err(e) = Self::handle_tcp_connection(
                        tunnel_context_clone,
                        stream,
                        local_addr,
                        remote_addr,
                    ).await {
                        error_utils::log_error(&e, "TCP连接处理");

                        // 更新失败统计
                        let mut stats_guard = stats_clone.lock().await;
                        stats_guard.failed_connections += 1;
                        stats_guard.active_connections = stats_guard.active_connections.saturating_sub(1);
                    } else {
                        // 连接正常结束，减少活跃连接数
                        let mut stats_guard = stats_clone.lock().await;
                        stats_guard.active_connections = stats_guard.active_connections.saturating_sub(1);
                    }
                });

                stats = stats_clone.lock().await.clone();

                // 定期输出统计信息
                if stats.total_connections % 100 == 0 {
                    debug!("TCP 连接统计: {}", stats);
                }
            }

            warn!("TCP 监听器已关闭");
        });

        debug!("TCP 连接处理任务启动完成");
        Ok(())
    }

    /// 处理单个 TCP 连接
    ///
    /// # 参数
    /// * `tunnel_context` - 隧道上下文
    /// * `stream` - TCP 流
    /// * `local_addr` - 本地地址
    /// * `remote_addr` - 远程地址
    ///
    /// # 返回值
    /// 如果处理成功则返回 `Ok(())`，否则返回错误
    async fn handle_tcp_connection<S>(
        tunnel_context: Arc<TunnelContext>,
        stream: S,
        local_addr: SocketAddr,
        remote_addr: SocketAddr,
    ) -> TunResult<()>
    where
        S: tokio::io::AsyncRead + tokio::io::AsyncWrite + Unpin + Send + 'static + Sync,
    {
        debug!("处理 TCP 连接 {} -> {}", local_addr, remote_addr);

        // 创建隧道映射信息
        let tunnel_mapper_info = Self::create_tunnel_mapper_info(
            local_addr.ip().to_string(),
            local_addr.port(),
            remote_addr.ip().to_string(),
            remote_addr.port(),
        );

        // 连接到服务器
        tunnel_context
            .tun_connect_server(tunnel_mapper_info, stream)
            .await
            .map_err(|e| TunError::TunnelConnectionError(format!("隧道连接失败: {}", e)))?;

        debug!("TCP 连接处理完成 {} -> {}", local_addr, remote_addr);
        Ok(())
    }

    /// 创建隧道映射信息
    ///
    /// # 参数
    /// * `source_addr` - 源地址
    /// * `source_port` - 源端口
    /// * `target_addr` - 目标地址
    /// * `target_port` - 目标端口
    ///
    /// # 返回值
    /// 返回配置好的 TunnelMapperInfo 实例
    fn create_tunnel_mapper_info(
        source_addr: String,
        source_port: u16,
        target_addr: String,
        target_port: u16,
    ) -> TunnelMapperInfo {
        TunnelMapperInfo {
            protocol: PackageProtocol::TCP,
            source_addr,
            source_port,
            target_addr,
            target_port,
            fake_target_addr: String::new(),
            fake_target_port: 0,
            process_name: get_process_name_by_port(source_port, PackageProtocol::TCP),
            matcher_name: String::new(),
            matcher_rule: String::new(),
            proxy_type: ProxyType::Proxy,
            active_time: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis(),
            temp_data: Vec::new(),
            direct_conn_priority: true,
            traffic_info: Arc::new(Default::default()),
        }
    }

    /// 获取配置信息
    ///
    /// # 返回值
    /// 返回当前的 TUN 配置
    pub fn config(&self) -> &TunConfig {
        &self.config
    }

    /// 更新配置
    ///
    /// # 参数
    /// * `config` - 新的配置
    pub fn update_config(&mut self, config: TunConfig) {
        debug!("更新 TCP 处理器配置");
        self.config = config;
    }

    /// 检查地址是否被封锁
    ///
    /// # 参数
    /// * `address` - 要检查的地址
    ///
    /// # 返回值
    /// 如果地址被封锁则返回 `true`，否则返回 `false`
    pub fn is_address_blocked(&self, address: &str) -> bool {
        self.config.is_dns_address_blocked(address)
    }
}

impl std::fmt::Display for TcpStats {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "TCP Stats - Total: {}, Active: {}, Blocked: {}, Failed: {}",
            self.total_connections, self.active_connections, self.blocked_connections, self.failed_connections
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{IpAddr, Ipv4Addr};

    #[test]
    fn test_tcp_handler_creation() {
        // 这里需要模拟 TunnelContext，在实际测试中需要创建 mock
        // let tunnel_context = Arc::new(mock_tunnel_context());
        // let config = TunConfig::default();
        // let handler = TcpHandler::new(tunnel_context, config);
        // assert!(handler.config().enable_dns_blocking);
    }

    #[test]
    fn test_create_tunnel_mapper_info() {
        let info = TcpHandler::create_tunnel_mapper_info(
            "***********".to_string(),
            8080,
            "*******".to_string(),
            53,
        );

        assert_eq!(info.protocol, PackageProtocol::TCP);
        assert_eq!(info.source_addr, "***********");
        assert_eq!(info.source_port, 8080);
        assert_eq!(info.target_addr, "*******");
        assert_eq!(info.target_port, 53);
        assert_eq!(info.proxy_type, ProxyType::Proxy);
        assert!(info.direct_conn_priority);
    }

    #[test]
    fn test_address_blocking() {
        let config = TunConfig::default();
        let tunnel_context = Arc::new(
            // mock_tunnel_context() // 在实际测试中需要创建 mock
        );
        // let handler = TcpHandler::new(tunnel_context, config);
        
        // assert!(handler.is_address_blocked("*******:853"));
        // assert!(!handler.is_address_blocked("*******:80"));
    }

    #[test]
    fn test_tcp_stats_display() {
        let stats = TcpStats {
            total_connections: 100,
            active_connections: 10,
            blocked_connections: 5,
            failed_connections: 2,
        };

        let display = format!("{}", stats);
        assert!(display.contains("Total: 100"));
        assert!(display.contains("Active: 10"));
        assert!(display.contains("Blocked: 5"));
        assert!(display.contains("Failed: 2"));
    }
}
